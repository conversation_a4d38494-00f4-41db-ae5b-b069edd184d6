﻿++Solution 'Talmatha.BuildingBlocks.V3' ‎ (7 of 7 projects)
i:{00000000-0000-0000-0000-000000000000}:Talmatha.BuildingBlocks.V3.sln
++src
i:{00000000-0000-0000-0000-000000000000}:src
++api
i:{67b31499-8c50-4551-8bbc-6a2b9eb7846d}:api
++Main
i:{1d77fbd0-a7b2-4fea-8583-91bbca601363}:Main
++Connected Services 
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>936
++Dependencies
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>938
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>931
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>934
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:>932
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:>933
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>935
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>930
++Properties
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\properties\
++Extensions
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\extensions\
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\extensions\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\extensions\
++internal_logs
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\internal_logs\
++internallog.txt
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\internal_logs\internallog.txt
++appsettings.Development.json
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\appsettings.development.json
++nlog.config
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\nlog.config
++Program.cs
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\program.cs
++systemConfiguration.json
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\systemconfiguration.json
++No service dependencies discovered
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>937
++Analyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>992
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>955
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>961
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:>942
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:>941
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>949
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>959
++Frameworks
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1097
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1070
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>1066
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:>1014
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:>1053
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>1026
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1013
++Packages
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1100
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1077
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>1071
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:>1023
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>1042
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1022
++Projects
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>943
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>940
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>939
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>945
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>944
++launchSettings.json
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\properties\launchsettings.json
++ServiceExtensions.cs
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\extensions\serviceextensions.cs
++ValidationErrorResponseFactory.cs
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\extensions\validationerrorresponsefactory.cs
++ValidationMiddleware.cs
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\work\newtemplate_elramady\src\apis\main\extensions\validationmiddleware.cs
++Microsoft.AspNetCore.Analyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.analyzers.dll
++Microsoft.AspNetCore.App.Analyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.app.analyzers.dll
++Microsoft.AspNetCore.App.CodeFixes
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.app.codefixes.dll
++Microsoft.AspNetCore.Components.Analyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\packs\microsoft.aspnetcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.AspNetCore.Mvc.Analyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.web\analyzers\cs\microsoft.aspnetcore.mvc.analyzers.dll
++Microsoft.AspNetCore.Razor.Utilities.Shared
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.razor\source-generators\microsoft.aspnetcore.razor.utilities.shared.dll
++Microsoft.CodeAnalysis.Analyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.Analyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.3.4\analyzers\dotnet\cs\microsoft.codeanalysis.csharp.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.CodeAnalysis.Razor.Compiler
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.razor\source-generators\microsoft.codeanalysis.razor.compiler.dll
++Microsoft.EntityFrameworkCore.Analyzers
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.3\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.3\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\.nuget\packages\microsoft.entityframeworkcore.analyzers\9.0.3\analyzers\dotnet\cs\microsoft.entityframeworkcore.analyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.ObjectPool
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.razor\source-generators\microsoft.extensions.objectpool.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.3\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Collections.Immutable
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk.razor\source-generators\system.collections.immutable.dll
++System.Text.Json.SourceGeneration
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.3\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.3\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\.nuget\packages\system.text.json\9.0.3\analyzers\dotnet\roslyn4.4\cs\system.text.json.sourcegeneration.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\9.0.0\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.AspNetCore.App
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1098
++Microsoft.NETCore.App
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1099
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1074
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>1068
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:>1020
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:>1058
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>1037
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1018
++Asp.Versioning.Mvc (8.1.0)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1101
++AspNetCoreRateLimit (5.0.0)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1105
++Marvin.Cache.Headers (7.1.0)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1106
++Microsoft.AspNetCore.Mvc.NewtonsoftJson (8.0.12)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1104
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>1076
++Microsoft.EntityFrameworkCore.Design (9.0.3)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1102
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1082
++Microsoft.Extensions.Caching.StackExchangeRedis (9.0.2)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1108
++NLog (5.4.0)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1107
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1096
++NuGet.CommandLine (6.14.0)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1109
++Swashbuckle.AspNetCore (7.3.1)
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>1103
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1084
++Abstraction
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>977
i:{3ad90848-6471-4410-a30e-c26d50b079a9}:Abstraction
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>952
++Application
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>970
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>946
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>950
i:{3ad90848-6471-4410-a30e-c26d50b079a9}:Application
++Infrastructure
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>984
i:{67b31499-8c50-4551-8bbc-6a2b9eb7846d}:Infrastructure
i:{bdb9e9cf-a3d0-4abf-aafd-601d48fb9012}:Infrastructure
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>956
++Presentation
i:{b41d9d63-a26c-4b34-9ae9-f95599c4376e}:>988
i:{bdb9e9cf-a3d0-4abf-aafd-601d48fb9012}:Presentation
++Data
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\data\
++Dto
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\dto\
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\dto\
++Logger
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\logger\
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\logger\
++Migrations
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\migrations\
++Repository
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\repository\
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\repository\
++Service
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\
++Catalog
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\catalog\
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\repository\catalog\
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\repository\catalog\
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\catalog\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\
++DemoEntityService.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\catalog\demoentityservice.cs
++ProductService.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\catalog\productservice.cs
++Identity
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\identity\
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\identity\
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\identity\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\
++BaseService.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\baseservice.cs
++IdentityServiceManager.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\identityservicemanager.cs
++ServiceManager.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\servicemanager.cs
++AssemblyReference.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\assemblyreference.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\assemblyreference.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\assemblyreference.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\assemblyreference.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\assemblyreference.cs
++InfrastructureDependencies.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\infrastructuredependencies.cs
++InfrastructureServicesRegisteration.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\infrastructureservicesregisteration.cs
++Configurations
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\data\configurations\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\configurations\
++AppDbContext.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\data\appdbcontext.cs
++AuditableDbContext.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\data\auditabledbcontext.cs
++DbContextFactory.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\data\dbcontextfactory.cs
++DemoEntity
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\dto\demoentity\
++LoggerManager.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\logger\loggermanager.cs
++20250513064512_Initial.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\migrations\20250513064512_initial.cs
++20250513091754_UpdateAuditColumns.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\migrations\20250513091754_updateauditcolumns.cs
++AppDbContextModelSnapshot.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\migrations\appdbcontextmodelsnapshot.cs
++GenericRepository.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\repository\genericrepository.cs
++RepositoryManager.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\repository\repositorymanager.cs
++AuthenticationIdentityService.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\identity\authenticationidentityservice.cs
++AuthorizationIdentityService.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\identity\authorizationidentityservice.cs
++UserManagmentIdentityService.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\service\identity\usermanagmentidentityservice.cs
++Microsoft.AspNetCore.Authentication.JwtBearer (9.0.3)
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1092
++Microsoft.AspNetCore.Identity.EntityFrameworkCore (9.0.3)
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1090
++Microsoft.EntityFrameworkCore (9.0.3)
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1079
++Microsoft.EntityFrameworkCore.SqlServer (9.0.3)
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1081
++Microsoft.EntityFrameworkCore.Tools (9.0.3)
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1094
++Microsoft.Extensions.Configuration.Binder (9.0.3)
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1089
++Swashbuckle.AspNetCore.Annotations (7.3.1)
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>1086
++Domain
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:>953
i:{3ad90848-6471-4410-a30e-c26d50b079a9}:Domain
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>948
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>947
++RoleConfig.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\data\configurations\roleconfig.cs
++Seed.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\data\configurations\seed.cs
++UserConfig.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\data\configurations\userconfig.cs
++DemoEntityDto.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\dto\demoentity\demoentitydto.cs
++DemoEntityValidation.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\dto\demoentity\demoentityvalidation.cs
++DemoProfile.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\dto\demoentity\demoprofile.cs
++20250513064512_Initial.Designer.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\migrations\20250513064512_initial.designer.cs
++20250513091754_UpdateAuditColumns.Designer.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\migrations\20250513091754_updateauditcolumns.designer.cs
++CategoryRepository.cs
i:{7ecca32e-25cf-48c5-99ee-ca8841c4112b}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\infrastructure\repository\catalog\categoryrepository.cs
++Bases
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\bases\
++AppControllerBase.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\bases\appcontrollerbase.cs
++BaseController.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\bases\basecontroller.cs
++Controllers
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\
++Cataloge
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\cataloge\
++CategoryController.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\cataloge\categorycontroller.cs
++DemoEntityController.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\cataloge\demoentitycontroller.cs
++ProductController.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\cataloge\productcontroller.cs
++AuthenticationController.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\identity\authenticationcontroller.cs
++AuthorzationController.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\identity\authorzationcontroller.cs
++UserManagementController.cs
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:c:\users\<USER>\work\newtemplate_elramady\src\infrastructure\presentation\controllers\identity\usermanagementcontroller.cs
++Microsoft.AspNetCore.JsonPatch (8.0.12)
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>1073
++Microsoft.AspNetCore.Mvc.Core (2.1.38)
i:{b04d0e38-035c-4824-bf7d-fd5b4ac4b313}:>1078
++Core
i:{67b31499-8c50-4551-8bbc-6a2b9eb7846d}:Core
++Entities
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\
++Base
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\base\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\base\
++Helpers
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\helpers\
++Products
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\products\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\products\
++Users
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\users\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\users\
++Microsoft.AspNetCore.Identity (2.3.1)
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:>1030
++Microsoft.Extensions.Identity.Stores (9.0.3)
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:>1032
++AduitedEntity.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\base\aduitedentity.cs
++BaseEntity.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\base\baseentity.cs
++CreationAuditedEntity.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\base\creationauditedentity.cs
++FullAuditedEntity.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\base\fullauditedentity.cs
++EditUserRolesRequest.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\helpers\edituserrolesrequest.cs
++JwtAuthResponse.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\helpers\jwtauthresponse.cs
++JwtSettings.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\helpers\jwtsettings.cs
++ManageUserRolesResponse.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\helpers\manageuserrolesresponse.cs
++Permission.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\helpers\permission.cs
++RoleClaim.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\helpers\roleclaim.cs
++UserClaimsModel.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\helpers\userclaimsmodel.cs
++Category.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\products\category.cs
++DemoEntity.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\products\demoentity.cs
++Product.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\products\product.cs
++Role.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\users\role.cs
++User.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\users\user.cs
++UserRefreshToken.cs
i:{7da6f654-6ffe-471e-b410-128bf67a176e}:c:\users\<USER>\work\newtemplate_elramady\src\core\domain\entities\users\userrefreshtoken.cs
++Resources
i:{3ad90848-6471-4410-a30e-c26d50b079a9}:Resources
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>957
++SharedResources.cs
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\users\<USER>\work\newtemplate_elramady\src\core\resources\sharedresources.cs
++SharedResourcesKey.cs
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\users\<USER>\work\newtemplate_elramady\src\core\resources\sharedresourceskey.cs
++SharedResources.ar-EG.resx
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\users\<USER>\work\newtemplate_elramady\src\core\resources\sharedresources.ar-eg.resx
++SharedResources.en-US.resx
i:{ea573e00-d1c2-4910-b924-fdaeb15bf931}:c:\users\<USER>\work\newtemplate_elramady\src\core\resources\sharedresources.en-us.resx
++Constants
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\constants\
++Contract
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\
++Response
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\response\
++Wappers
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\wappers\
++ModulePermissions
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\constants\modulepermissions\
++Claims.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\constants\claims.cs
++CustomClaimTypes.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\constants\customclaimtypes.cs
++Roles.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\constants\roles.cs
++BaseDto.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\dto\basedto.cs
++BaseListDto.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\dto\baselistdto.cs
++DistributedCacheExtensions.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\extensions\distributedcacheextensions.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\extensions\distributedcacheextensions.cs
++OrderQueryBuilder.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\extensions\orderquerybuilder.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\extensions\orderquerybuilder.cs
++QueryExtensions.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\extensions\queryextensions.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\extensions\queryextensions.cs
++BaseResponse.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\response\baseresponse.cs
++BaseResponseHandler.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\response\baseresponsehandler.cs
++Error.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\response\error.cs
++PaginatedResult.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\wappers\paginatedresult.cs
++QueryableExtentions.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\wappers\queryableextentions.cs
++System.IdentityModel.Tokens.Jwt (8.0.1)
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>1046
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1031
++System.Linq.Dynamic.Core (1.6.0.2)
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:>1052
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1059
++ProductsPermission.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\constants\modulepermissions\productspermission.cs
++ILoggerManager.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\logger\iloggermanager.cs
++IGenericRepository.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\repository\igenericrepository.cs
++IRepositoryManager.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\repository\irepositorymanager.cs
++IBaseService.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\ibaseservice.cs
++IServiceManager.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\iservicemanager.cs
++ICategoryRepository.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\repository\catalog\icategoryrepository.cs
++IDemoEntityService.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\catalog\idemoentityservice.cs
++IProductService.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\catalog\iproductservice.cs
++IAuthenticationService.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\identity\iauthenticationservice.cs
++IAuthorizationService.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\identity\iauthorizationservice.cs
++IIdentityServiceManager.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\identity\iidentityservicemanager.cs
++IUserManagmentService.cs
i:{f2443021-0057-4d98-9975-5fb15fdf359b}:c:\users\<USER>\work\newtemplate_elramady\src\core\abstraction\contract\service\identity\iusermanagmentservice.cs
++Common
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\
++Features
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\
++Categories
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\categories\
++Commands
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\commands\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\commands\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\
++Dtos
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\dtos\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\dtos\
++Queries
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\queries\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\queries\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\queries\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\
++Validation
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\validation\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\validation\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\validation\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\validation\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\validation\
++Get
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\queries\get\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\queries\get\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\get\
++GetQuery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\queries\get\getquery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\queries\get\getquery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\get\getquery.cs
++GetQueryHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\queries\get\getqueryhandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\queries\get\getqueryhandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\get\getqueryhandler.cs
++List
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\queries\list\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\queries\list\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\list\
++ListQuery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\queries\list\listquery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\queries\list\listquery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\list\listquery.cs
++ListQueryHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\queries\list\listqueryhandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\queries\list\listqueryhandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\list\listqueryhandler.cs
++Mapping
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\
++ApplicationDependencies.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\applicationdependencies.cs
++Abstracts
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\base\abstracts\
++Behaviors
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\behaviors\
++MiddleWare
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\middleware\
++Add
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\commands\add\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\add\
++AddCategoryDto.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\dtos\addcategorydto.cs
++CategoryDto.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\dtos\categorydto.cs
++SingleCategoryResponse.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\dtos\singlecategoryresponse.cs
++AddValidation.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\validation\addvalidation.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\validation\addvalidation.cs
++BaseValidation.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\validation\basevalidation.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\validation\basevalidation.cs
++Delete
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\delete\
++Edit
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\edit\
++AddProductDto.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\dtos\addproductdto.cs
++EditProductDto.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\dtos\editproductdto.cs
++ProductDto.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\dtos\productdto.cs
++SingleProductResponse.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\dtos\singleproductresponse.cs
++EditValidation.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\validation\editvalidation.cs
++Authentications
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\
++Authorizations
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\authorizations\
++AddCategoryMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\categories\addcategorymapping.cs
++CategoriesProfile.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\categories\categoriesprofile.cs
++GetCategroyMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\categories\getcategroymapping.cs
++AddProductMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\products\addproductmapping.cs
++EditProductMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\products\editproductmapping.cs
++GetProductMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\products\getproductmapping.cs
++ProductsProfile.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\products\productsprofile.cs
++AutoMapper (14.0.0)
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1055
++FluentValidation (11.11.0)
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1051
++FluentValidation.DependencyInjectionExtensions (11.11.0)
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1025
++MediatR (12.4.1)
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1045
++Microsoft.AspNetCore.Http.Abstractions (2.3.0)
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1064
++Microsoft.Extensions.DependencyInjection.Abstractions (9.0.3)
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1036
++Microsoft.Extensions.Localization.Abstractions (9.0.5)
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1063
++Microsoft.Extensions.Options.ConfigurationExtensions (9.0.3)
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:>1039
++ICommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\base\abstracts\icommand.cs
++ICommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\base\abstracts\icommandhandler.cs
++IQuery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\base\abstracts\iquery.cs
++IQueryHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\base\abstracts\iqueryhandler.cs
++ValidationBehavior.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\behaviors\validationbehavior.cs
++PDFConfiguration.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\configurations\pdfconfiguration.cs
++ErrorHandlerMiddleWare.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\common\middleware\errorhandlermiddleware.cs
++AddCategoryCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\commands\add\addcategorycommand.cs
++AddCategoryCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\categories\commands\add\addcategorycommandhandler.cs
++AddProductCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\add\addproductcommand.cs
++AddProductCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\add\addproductcommandhandler.cs
++DeleteProductCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\delete\deleteproductcommand.cs
++DeleteProductCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\delete\deleteproductcommandhandler.cs
++EditProductCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\edit\editproductcommand.cs
++EditProductCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\catalog\products\commands\edit\editproductcommandhandler.cs
++QueryMapping
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\authorizations\querymapping\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\users\querymapping\
++AuthorizationProfile.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\authorizations\authorizationprofile.cs
++CommandMapping
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\users\commandmapping\
++UserProfile.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\users\userprofile.cs
++RefreshToken
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\commands\refreshtoken\
++SignIn
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\commands\signin\
++ValidateAccessToken
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\queries\validateaccesstoken\
++AccessTokenQueryValidatior.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\validation\accesstokenqueryvalidatior.cs
++RefreshTokenValidatior.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\validation\refreshtokenvalidatior.cs
++SignInValidatior.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\validation\signinvalidatior.cs
++AddRole
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\addrole\
++DeleteRole
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\deleterole\
++EditRole
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\editrole\
++GetClaimList
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getclaimlist\
++GetRoleById
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getrolebyid\
++GetRoleList
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getrolelist\
++Responses
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\responses\
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\responses\
++AddRoleValidator.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\validation\addrolevalidator.cs
++EditRoleValidator.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\validation\editrolevalidator.cs
++AddUser
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\adduser\
++ChangePassword
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\changepassword\
++DeleteUser
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\deleteuser\
++EditUser
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\edituser\
++EditUserRoles
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\edituserroles\
++GetUserRoles
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\getuserroles\
++AddUserValidatior.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\validation\adduservalidatior.cs
++ChangeUserPasswordValidatior.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\validation\changeuserpasswordvalidatior.cs
++EditUserValidatior.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\validation\edituservalidatior.cs
++GetRoleByIdMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\authorizations\querymapping\getrolebyidmapping.cs
++GetRoleListMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\authorizations\querymapping\getrolelistmapping.cs
++AddUserMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\users\commandmapping\addusermapping.cs
++EditUserMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\users\commandmapping\editusermapping.cs
++GetUserByIdMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\users\querymapping\getuserbyidmapping.cs
++GetUserPaginatedListMapping.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\mapping\identity\users\querymapping\getuserpaginatedlistmapping.cs
++RefreshTokenCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\commands\refreshtoken\refreshtokencommand.cs
++RefreshTokenCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\commands\refreshtoken\refreshtokencommandhandler.cs
++SignInCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\commands\signin\signincommand.cs
++SignInCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\commands\signin\signincommandhandler.cs
++AccessTokenQuery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\queries\validateaccesstoken\accesstokenquery.cs
++ValidateAccessTokenQueryHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authentications\queries\validateaccesstoken\validateaccesstokenqueryhandler.cs
++AddRoleCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\addrole\addrolecommand.cs
++AddRoleCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\addrole\addrolecommandhandler.cs
++DeleteRoleCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\deleterole\deleterolecommand.cs
++DeleteRoleCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\deleterole\deleterolecommandhandler.cs
++EditRoleCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\editrole\editrolecommand.cs
++EditRoleCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\commands\editrole\editrolecommandhandler.cs
++GetClaimListQuery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getclaimlist\getclaimlistquery.cs
++GetClaimListQueryHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getclaimlist\getclaimlistqueryhandler.cs
++GetRoleByIdQuery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getrolebyid\getrolebyidquery.cs
++GetRoleByIdQueryHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getrolebyid\getrolebyidqueryhandler.cs
++GetRoleListQuery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getrolelist\getrolelistquery.cs
++GetRoleListQueryHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\getrolelist\getrolelistqueryhandler.cs
++GetRoleByIdResponse.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\responses\getrolebyidresponse.cs
++GetRoleClaimsResponse.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\responses\getroleclaimsresponse.cs
++GetRoleListResponse.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\authorizations\queries\responses\getrolelistresponse.cs
++AddUserCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\adduser\addusercommand.cs
++AddUserCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\adduser\addusercommandhandler.cs
++ChangePasswordCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\changepassword\changepasswordcommand.cs
++ChangePasswordCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\changepassword\changepasswordcommandhandler.cs
++DeleteUserCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\deleteuser\deleteusercommand.cs
++DeleteUserCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\deleteuser\deleteusercommandhandler.cs
++EditUserCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\edituser\editusercommand.cs
++EditUserCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\edituser\editusercommandhandler.cs
++EditUserRolesCommand.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\edituserroles\edituserrolescommand.cs
++EditUserRolesCommandHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\commands\edituserroles\edituserrolescommandhandler.cs
++GetUserRolesQuery.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\getuserroles\getuserrolesquery.cs
++GetUserRolesQueryHandler.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\getuserroles\getuserrolesqueryhandler.cs
++GetUserListResponse.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\responses\getuserlistresponse.cs
++GetUserResponse.cs
i:{971b62f8-ebad-4229-b031-6f8b1458ce22}:c:\users\<USER>\work\newtemplate_elramady\src\core\application\features\identity\users\queries\responses\getuserresponse.cs
