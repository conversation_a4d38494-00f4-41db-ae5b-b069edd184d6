{"format": 1, "restore": {"C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Infrastructure\\Infrastructure\\Infrastructure.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Abstraction\\Abstraction.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Abstraction\\Abstraction.csproj", "projectName": "Abstraction", "projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Abstraction\\Abstraction.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Abstraction\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1008", "NU1901", "NU1902", "NU1903", "NU1904"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.1, )", "versionCentrallyManaged": true}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[*******, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Asp.Versioning.Mvc": "8.1.0", "Aspire.Hosting.AppHost": "9.1.0", "AspNetCoreRateLimit": "5.0.0", "AutoMapper": "14.0.0", "coverlet.collector": "6.0.4", "FluentValidation": "11.11.0", "FluentValidation.DependencyInjectionExtensions": "11.11.0", "Marvin.Cache.Headers": "7.1.0", "MediatR": "12.4.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.3", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.Identity": "2.3.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.3", "Microsoft.AspNetCore.JsonPatch": "8.0.12", "Microsoft.AspNetCore.Mvc.Core": "2.1.38", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.12", "Microsoft.AspNetCore.OpenApi": "9.0.3", "Microsoft.EntityFrameworkCore": "9.0.3", "Microsoft.EntityFrameworkCore.Design": "9.0.3", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.3", "Microsoft.EntityFrameworkCore.Tools": "9.0.3", "Microsoft.Extensions.Caching.StackExchangeRedis": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Http.Resilience": "9.3.0", "Microsoft.Extensions.Identity.Stores": "9.0.3", "Microsoft.Extensions.Localization.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3", "Microsoft.Extensions.ServiceDiscovery": "9.1.0", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.0", "MMLib.SwaggerForOcelot": "8.3.2", "Newtonsoft.Json": "13.0.3", "NLog": "5.4.0", "NuGet.CommandLine": "6.14.0", "Ocelot": "23.4.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.11.2", "OpenTelemetry.Extensions.Hosting": "1.11.2", "OpenTelemetry.Instrumentation.AspNetCore": "1.11.1", "OpenTelemetry.Instrumentation.Http": "1.11.1", "OpenTelemetry.Instrumentation.Runtime": "1.11.1", "Swashbuckle.AspNetCore": "7.3.1", "Swashbuckle.AspNetCore.Annotations": "7.3.1", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Linq.Dynamic.Core": "*******", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.0.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Application\\Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Application\\Application.csproj", "projectName": "Application", "projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Application\\Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Application\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Abstraction\\Abstraction.csproj": {"projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Abstraction\\Abstraction.csproj"}, "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj"}, "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Resources\\Resources.csproj": {"projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Resources\\Resources.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1008", "NU1901", "NU1902", "NU1903", "NU1904"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )", "versionCentrallyManaged": true}, "FluentValidation": {"target": "Package", "version": "[11.11.0, )", "versionCentrallyManaged": true}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.11.0, )", "versionCentrallyManaged": true}, "MediatR": {"target": "Package", "version": "[12.4.1, )", "versionCentrallyManaged": true}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.3.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Localization.Abstractions": {"target": "Package", "version": "[9.0.5, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.1, )", "versionCentrallyManaged": true}, "System.Linq.Dynamic.Core": {"target": "Package", "version": "[*******, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Asp.Versioning.Mvc": "8.1.0", "Aspire.Hosting.AppHost": "9.1.0", "AspNetCoreRateLimit": "5.0.0", "AutoMapper": "14.0.0", "coverlet.collector": "6.0.4", "FluentValidation": "11.11.0", "FluentValidation.DependencyInjectionExtensions": "11.11.0", "Marvin.Cache.Headers": "7.1.0", "MediatR": "12.4.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.3", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.Identity": "2.3.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.3", "Microsoft.AspNetCore.JsonPatch": "8.0.12", "Microsoft.AspNetCore.Mvc.Core": "2.1.38", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.12", "Microsoft.AspNetCore.OpenApi": "9.0.3", "Microsoft.EntityFrameworkCore": "9.0.3", "Microsoft.EntityFrameworkCore.Design": "9.0.3", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.3", "Microsoft.EntityFrameworkCore.Tools": "9.0.3", "Microsoft.Extensions.Caching.StackExchangeRedis": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Http.Resilience": "9.3.0", "Microsoft.Extensions.Identity.Stores": "9.0.3", "Microsoft.Extensions.Localization.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3", "Microsoft.Extensions.ServiceDiscovery": "9.1.0", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.0", "MMLib.SwaggerForOcelot": "8.3.2", "Newtonsoft.Json": "13.0.3", "NLog": "5.4.0", "NuGet.CommandLine": "6.14.0", "Ocelot": "23.4.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.11.2", "OpenTelemetry.Extensions.Hosting": "1.11.2", "OpenTelemetry.Instrumentation.AspNetCore": "1.11.1", "OpenTelemetry.Instrumentation.Http": "1.11.1", "OpenTelemetry.Instrumentation.Runtime": "1.11.1", "Swashbuckle.AspNetCore": "7.3.1", "Swashbuckle.AspNetCore.Annotations": "7.3.1", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Linq.Dynamic.Core": "*******", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.0.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj", "projectName": "Domain", "projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1008", "NU1901", "NU1902", "NU1903", "NU1904"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.3.1, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Identity.Stores": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Asp.Versioning.Mvc": "8.1.0", "Aspire.Hosting.AppHost": "9.1.0", "AspNetCoreRateLimit": "5.0.0", "AutoMapper": "14.0.0", "coverlet.collector": "6.0.4", "FluentValidation": "11.11.0", "FluentValidation.DependencyInjectionExtensions": "11.11.0", "Marvin.Cache.Headers": "7.1.0", "MediatR": "12.4.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.3", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.Identity": "2.3.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.3", "Microsoft.AspNetCore.JsonPatch": "8.0.12", "Microsoft.AspNetCore.Mvc.Core": "2.1.38", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.12", "Microsoft.AspNetCore.OpenApi": "9.0.3", "Microsoft.EntityFrameworkCore": "9.0.3", "Microsoft.EntityFrameworkCore.Design": "9.0.3", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.3", "Microsoft.EntityFrameworkCore.Tools": "9.0.3", "Microsoft.Extensions.Caching.StackExchangeRedis": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Http.Resilience": "9.3.0", "Microsoft.Extensions.Identity.Stores": "9.0.3", "Microsoft.Extensions.Localization.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3", "Microsoft.Extensions.ServiceDiscovery": "9.1.0", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.0", "MMLib.SwaggerForOcelot": "8.3.2", "Newtonsoft.Json": "13.0.3", "NLog": "5.4.0", "NuGet.CommandLine": "6.14.0", "Ocelot": "23.4.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.11.2", "OpenTelemetry.Extensions.Hosting": "1.11.2", "OpenTelemetry.Instrumentation.AspNetCore": "1.11.1", "OpenTelemetry.Instrumentation.Http": "1.11.1", "OpenTelemetry.Instrumentation.Runtime": "1.11.1", "Swashbuckle.AspNetCore": "7.3.1", "Swashbuckle.AspNetCore.Annotations": "7.3.1", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Linq.Dynamic.Core": "*******", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.0.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Resources\\Resources.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Resources\\Resources.csproj", "projectName": "Resources", "projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Resources\\Resources.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Resources\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1008", "NU1901", "NU1902", "NU1903", "NU1904"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "centralPackageVersions": {"Asp.Versioning.Mvc": "8.1.0", "Aspire.Hosting.AppHost": "9.1.0", "AspNetCoreRateLimit": "5.0.0", "AutoMapper": "14.0.0", "coverlet.collector": "6.0.4", "FluentValidation": "11.11.0", "FluentValidation.DependencyInjectionExtensions": "11.11.0", "Marvin.Cache.Headers": "7.1.0", "MediatR": "12.4.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.3", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.Identity": "2.3.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.3", "Microsoft.AspNetCore.JsonPatch": "8.0.12", "Microsoft.AspNetCore.Mvc.Core": "2.1.38", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.12", "Microsoft.AspNetCore.OpenApi": "9.0.3", "Microsoft.EntityFrameworkCore": "9.0.3", "Microsoft.EntityFrameworkCore.Design": "9.0.3", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.3", "Microsoft.EntityFrameworkCore.Tools": "9.0.3", "Microsoft.Extensions.Caching.StackExchangeRedis": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Http.Resilience": "9.3.0", "Microsoft.Extensions.Identity.Stores": "9.0.3", "Microsoft.Extensions.Localization.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3", "Microsoft.Extensions.ServiceDiscovery": "9.1.0", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.0", "MMLib.SwaggerForOcelot": "8.3.2", "Newtonsoft.Json": "13.0.3", "NLog": "5.4.0", "NuGet.CommandLine": "6.14.0", "Ocelot": "23.4.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.11.2", "OpenTelemetry.Extensions.Hosting": "1.11.2", "OpenTelemetry.Instrumentation.AspNetCore": "1.11.1", "OpenTelemetry.Instrumentation.Http": "1.11.1", "OpenTelemetry.Instrumentation.Runtime": "1.11.1", "Swashbuckle.AspNetCore": "7.3.1", "Swashbuckle.AspNetCore.Annotations": "7.3.1", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Linq.Dynamic.Core": "*******", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.0.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Infrastructure\\Infrastructure\\Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Infrastructure\\Infrastructure\\Infrastructure.csproj", "projectName": "Infrastructure", "projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Infrastructure\\Infrastructure\\Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Infrastructure\\Infrastructure\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Application\\Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Application\\Application.csproj"}, "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Work\\NewTemplate_ElRamady\\src\\Core\\Domain\\Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"], "warnNotAsError": ["NU1008", "NU1901", "NU1902", "NU1903", "NU1904"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[9.0.3, )", "versionCentrallyManaged": true}, "NLog": {"target": "Package", "version": "[5.4.0, )", "versionCentrallyManaged": true}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[7.3.1, )", "versionCentrallyManaged": true}, "Swashbuckle.AspNetCore.Annotations": {"target": "Package", "version": "[7.3.1, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Asp.Versioning.Mvc": "8.1.0", "Aspire.Hosting.AppHost": "9.1.0", "AspNetCoreRateLimit": "5.0.0", "AutoMapper": "14.0.0", "coverlet.collector": "6.0.4", "FluentValidation": "11.11.0", "FluentValidation.DependencyInjectionExtensions": "11.11.0", "Marvin.Cache.Headers": "7.1.0", "MediatR": "12.4.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.3", "Microsoft.AspNetCore.Http.Abstractions": "2.3.0", "Microsoft.AspNetCore.Identity": "2.3.1", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "9.0.3", "Microsoft.AspNetCore.JsonPatch": "8.0.12", "Microsoft.AspNetCore.Mvc.Core": "2.1.38", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.12", "Microsoft.AspNetCore.OpenApi": "9.0.3", "Microsoft.EntityFrameworkCore": "9.0.3", "Microsoft.EntityFrameworkCore.Design": "9.0.3", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.3", "Microsoft.EntityFrameworkCore.Tools": "9.0.3", "Microsoft.Extensions.Caching.StackExchangeRedis": "9.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.3", "Microsoft.Extensions.Configuration.Binder": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.3", "Microsoft.Extensions.Http.Resilience": "9.3.0", "Microsoft.Extensions.Identity.Stores": "9.0.3", "Microsoft.Extensions.Localization.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.3", "Microsoft.Extensions.ServiceDiscovery": "9.1.0", "Microsoft.NET.Test.Sdk": "17.13.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.21.0", "MMLib.SwaggerForOcelot": "8.3.2", "Newtonsoft.Json": "13.0.3", "NLog": "5.4.0", "NuGet.CommandLine": "6.14.0", "Ocelot": "23.4.3", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.11.2", "OpenTelemetry.Extensions.Hosting": "1.11.2", "OpenTelemetry.Instrumentation.AspNetCore": "1.11.1", "OpenTelemetry.Instrumentation.Http": "1.11.1", "OpenTelemetry.Instrumentation.Runtime": "1.11.1", "Swashbuckle.AspNetCore": "7.3.1", "Swashbuckle.AspNetCore.Annotations": "7.3.1", "System.IdentityModel.Tokens.Jwt": "8.0.1", "System.Linq.Dynamic.Core": "*******", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.0.2"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}